[package]
authors = ["<PERSON><PERSON><PERSON><PERSON> <n<PERSON><PERSON><PERSON>@gmail.com>"]
description = "A cli tool to remove comments from code. Supports multiple languages."
edition = "2021"
keywords = ["comments", "cli", "pre-commit"]
license = "MIT"
name = "uncomment"
readme = "README.md"
repository = "https://github.com/Goldziher/uncomment"
version = "1.0.6"

[dependencies]
clap = { version = "4.5.39", features = ["derive"] }
glob = "0.3.2"
regex = "1.11.1"
tempfile = "3.20.0"
ignore = "0.4.23"
dirs = "6.0.0"
dirs-next = "2.0.0"

[[bin]]
name = "uncomment"
path = "src/main.rs"
